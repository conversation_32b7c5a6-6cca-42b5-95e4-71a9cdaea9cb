package data.keyless.lock

import core.http.client.HttpClient
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.keyless.utils.bearer
import data.keyless.utils.delete
import data.keyless.utils.get

class LockRepository(
    private val client: HttpClient,
    private val logger: Logger,
    private val hostname: String
) {
    suspend fun adminDeleteKeylessLock(request: AdminKeylessLockDeleteRequest) = logger.async {
        val url = "$hostname/locks/delete/${request.lockId}"
        val httpRequest = delete(url, bearer(request.token))

        return@async client.request(httpRequest).value(AdminKeylessLockDeleteResponse.serializer())
    }

    suspend fun adminLocks(request: AdminLocksGetRequest) = logger.async {
        val url = "$hostname/admin/locks/chinese?page=${request.page}&per_page=${request.perPage}&keyword=${request.keyword}"
        val httpRequest = get(url, bearer(request.token))

        return@async client.request(httpRequest).value(AdminLocksResponse.serializer())
    }
}

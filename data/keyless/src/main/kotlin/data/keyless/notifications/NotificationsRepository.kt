package data.keyless.notifications

import core.common.serialization.json
import core.http.client.HttpClient
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.keyless.utils.bearer
import data.keyless.utils.get
import data.keyless.utils.post
import kotlinx.serialization.json.encodeToJsonElement

class NotificationsRepository(
    private val client: HttpClient,
    private val logger: Logger,
    private val hostname: String
) {

    suspend fun getNotifications(request: NotificationsGetRequest) = logger.async {
        val url = "$hostname/user/notifications?page=${request.page}"
        val httpRequest = get(url, bearer(request.token))

        return@async client.request(httpRequest).value(NotificationsGetResponse.serializer())
    }

    suspend fun readNotifications(request: ReadNotificationsPostRequest) = logger.async {
        val url = "$hostname/user/read-notifications"
        val httpRequest = post(url, json.encodeToJsonElement(mapOf<String, String>()), bearer(request.token))

        return@async client.request(httpRequest).value(ReadNotificationsResponse.serializer())
    }
}

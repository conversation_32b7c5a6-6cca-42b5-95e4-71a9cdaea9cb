package data.keyless.notifications

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Request model for notifications GET operation.
 * Used for fetching paginated list of user notifications.
 */
@Serializable
data class NotificationsGetRequest(
    val page: Int,
    val token: String
)

/**
 * Response model for notifications operation.
 * Contains paginated list of notifications with metadata.
 */
@Serializable
data class NotificationsGetResponse(
    val success: Boolean = false,
    @SerialName("notifcations")
    val notifications: List<NotificationItem> = emptyList(),
    val total: Int = 0,
    @SerialName("resultsPerPage")
    val resultsPerPage: Int = 0
)

/**
 * Individual notification item containing notification details.
 */
@Serializable
data class NotificationItem(
    @SerialName("_id")
    val id: String = "",
    @SerialName("notification_type")
    val notificationType: String = "",
    @SerialName("user_id")
    val userId: String = "",
    val content: String = "",
    @SerialName("image_url")
    val imageUrl: String = "",
    @SerialName("created_at")
    val createdAt: String = "",
    @SerialName("__v")
    val version: String = "",
    val status: Int = 0
)

/**
 * Request model for read notifications POST operation.
 * Used for marking all notifications as read.
 */
@Serializable
data class ReadNotificationsPostRequest(
    val token: String
)

/**
 * Response model for read notifications operation.
 * Contains success status and message.
 */
@Serializable
data class ReadNotificationsResponse(
    val success: Boolean = false,
    @SerialName("force_update")
    val forceUpdate: Boolean = false,
    @SerialName("force_password_require")
    val forcePasswordRequire: Boolean = false,
    @SerialName("due_date")
    val dueDate: String = "",
    val timezone: String = "",
    @SerialName("timezone_name")
    val timezoneName: String = "",
    val message: String = ""
)

package data.keyless.notifications

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Request model for notifications GET operation.
 * Used for fetching paginated list of user notifications.
 */
@Serializable
data class NotificationsGetRequest(
    val page: Int,
    val token: String
)

/**
 * Response model for notifications operation.
 * Contains paginated list of notifications with metadata.
 */
@Serializable
data class NotificationsGetResponse(
    val success: Boolean = false,
    @SerialName("notifcations")
    val notifications: List<NotificationItem> = emptyList(),
    val total: Int = 0
)

/**
 * Individual notification item containing only the properties used in the UI.
 */
@Serializable
data class NotificationItem(
    val content: String = "",
    @SerialName("created_at")
    val createdAt: String = "",
    val status: Int = 0
)

/**
 * Request model for read notifications POST operation.
 * Used for marking all notifications as read.
 */
@Serializable
data class ReadNotificationsPostRequest(
    val token: String
)

/**
 * Response model for read notifications operation.
 * Contains only the essential response fields used in the implementation.
 */
@Serializable
data class ReadNotificationsResponse(
    val success: Boolean = false,
    val message: String = ""
)

package data.common

import data.common.preferences.Preferences
import data.common.utils.ensureEndsWith
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

fun Instant.device() = toLocalDateTime(TimeZone.currentSystemDefault())
fun Instant.isAfter(other: Instant) = this > other
fun Instant.isBefore(other: Instant) = this < other

val Instant.localDate get() = toLocalDateTime(TimeZone.currentSystemDefault()).date
fun LocalDate.isAfter(other: LocalDate) = this > other
fun LocalDate.isBefore(other: LocalDate) = this < other

fun now(): Instant = Clock.System.now()
fun lastServerTime() = runCatching { Instant.parseFormatted(Preferences.lastOnlineLocksFetchDateTime.get()) }.getOrNull()

fun Instant.Companion.parseFormatted(str: String): Instant? {
    val regular = runCatching { parse(str) }.getOrNull()
    if (regular != null) return regular

    val date = str.split("+").first().replace("0000", "Z").replace(".000", "").ensureEndsWith("Z")
    return runCatching { parse(date) }.onFailure { it.printStackTrace() }.getOrNull()
}
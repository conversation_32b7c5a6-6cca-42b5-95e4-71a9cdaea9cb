package data.company.repositories

import core.http.client.HttpResponse

interface CompanyStaffRemoteRepository {
    suspend fun getAllStaff(authentication: String): HttpResponse

    suspend fun getRoles(authentication: String): HttpResponse

    suspend fun addStaff(
        countryCode: String,
        mobileNumber: String,
        email: String,
        userName: String,
        firstName: String,
        lastName: String,
        role: String,
        passportNumber: String,
        authentication: String
    ): HttpResponse

    suspend fun updateStaff(
        staffId: String,
        countryCode: String,
        mobileNumber: String,
        email: String,
        userName: String,
        firstName: String,
        lastName: String,
        role: String,
        passportNumber: String,
        authentication: String
    ): HttpResponse
}
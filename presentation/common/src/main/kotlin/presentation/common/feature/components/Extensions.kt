package presentation.common.feature.components

import android.content.Context
import android.os.Bundle
import android.util.LayoutDirection
import androidx.annotation.IdRes
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.core.os.ConfigurationCompat
import androidx.navigation.NavController
import core.common.message.Message
import data.common.utils.ensureEndsWith
import keyless.presentation.common.R
import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime
import java.text.SimpleDateFormat
import java.util.Locale

fun NavController.safeNavigate(
    @IdRes currentDestinationId: Int,
    @IdRes id: Int,
    args: Bundle? = null
) {
    if (currentDestinationId == currentDestination?.id) {
        navigate(id, args)
    }
}

@Composable
fun Message.localized(): String {
    val context = LocalContext.current
    return if (resId == null) {
        if (context.resources.configuration.locales[0].language == "ar") ar else en
    } else {
        val args = args.mapText()
        context.getString(resId!!, *args)
    }
}

fun Context.isRtl() : Boolean {
    val config = resources.configuration.layoutDirection
    return LayoutDirection.RTL == config
}

@Composable
private fun Array<out Any>.mapText(): Array<out Any> = map {
    when (it) {
        is LocalDateTime -> it.represent()
        is LocalDate -> it.represent()
        else -> it
    }
}.toTypedArray()

@Composable
fun LocalDateTime.represent(): String {
    return fixForRepresent().representDateTime()
}

@Composable
fun LocalDate.represent(): String {
    return toString().representDate()
}

@Composable
fun String.representDate(): String {
    val formatter = SimpleDateFormat("yyyy-MM-dd", getLocale())
    val date = runCatching { formatter.parse(this) }.getOrNull() ?: return ""
    formatter.applyPattern("E${stringResource(id = R.string.comma)} d MMM")

    return formatter.format(date)
}

@Composable
private fun String.representDateTime(): String {
    val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm", getLocale())
    val date = runCatching { formatter.parse(this) }.onFailure { it.printStackTrace() }.getOrNull() ?: return ""
    formatter.applyPattern("E${stringResource(id = R.string.comma)} d MMM h:mm a")

    return formatter.format(date)
}
private fun LocalDateTime.fixForRepresent(): String {
    return if (time.second == 0) "$date ${time.toString().ensureEndsWith(":00")}" else "$date $time"
}
@Composable
@ReadOnlyComposable
fun getLocale(): Locale? {
    val configuration = LocalConfiguration.current
    return ConfigurationCompat.getLocales(configuration).get(0)
}

@Composable
fun Int.toDp(): Dp {
    return with(LocalDensity.current) { <EMAIL>() }
}
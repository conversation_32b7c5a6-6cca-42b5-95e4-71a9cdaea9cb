package presentation.home.notifications.domain.usecases

import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import presentation.common.domain.repositories.SideEffectsRepository
import presentation.home.notifications.domain.SideEffect
import presentation.home.notifications.domain.UserEvent

internal class OnBackClickUseCase(
    private val sideEffect: SideEffectsRepository<SideEffect>,
    private val logger: Logger
) {

    suspend fun execute(event: UserEvent.OnBackClick) = logger.async {
        // Original implementation sets result code 54 and finishes activity
        // This will be handled by the presentation layer (Activity/Fragment)
        sideEffect.emit(SideEffect.NavigateBack)
    }
}

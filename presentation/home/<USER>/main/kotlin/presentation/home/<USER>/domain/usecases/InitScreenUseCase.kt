package presentation.home.notifications.domain.usecases

import core.common.error.KError
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.keyless.notifications.NotificationsRepository
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository
import presentation.home.notifications.domain.ScreenData
import presentation.home.notifications.domain.ScreenEvent
import presentation.home.notifications.domain.SideEffect
import presentation.home.notifications.domain.notificationsGetRequest
import presentation.home.notifications.domain.readNotificationsRequest

internal class InitScreenUseCase(
    private val notificationsRepository: NotificationsRepository,
    private val screen: ScreenDataRepository<ScreenData>,
    private val sideEffect: SideEffectsRepository<SideEffect>,
    private val logger: Logger,
    private val status: StatusRepository
) {

    suspend fun execute(event: ScreenEvent.Init) = logger.async {
        val result = status.execute(
            loading = Message.fromString("Loading notifications..."),
            success = Message.fromString(""),
            job = { job(event) }
        )

        if (result is AsyncResult.Fail) {
            sideEffect.emit(SideEffect.ShowError(result.error.message.text()))
        }
    }

    private suspend fun job(event: ScreenEvent.Init) = logger.async {
        fetchNotifications(event)
    }

    private suspend fun fetchNotifications(event: ScreenEvent.Init) = logger.async {
        val request = event.notificationsGetRequest()
        val response = notificationsRepository.getNotifications(request)
        
        if (response.success) {
            handleNotificationsSuccess(response)
            markNotificationsAsRead()
        } else {
            throw KError.Info(Message.fromString("Failed to load notifications"))
        }
    }

    private suspend fun handleNotificationsSuccess(response: data.keyless.notifications.NotificationsGetResponse) = logger.async {
        val isLastPage = response.notifications.size >= response.total
        
        screen.update { currentData ->
            currentData.copy(
                notifications = response.notifications,
                currentPage = 1,
                isLastPage = isLastPage,
                isLoadingMore = false,
                totalNotifications = response.total
            )
        }
    }

    private suspend fun markNotificationsAsRead() = logger.async {
        try {
            val request = readNotificationsRequest()
            notificationsRepository.readNotifications(request)
            // Note: Original implementation doesn't handle read notifications response
            // It just calls the API without processing the result
        } catch (e: Exception) {
            // Silently handle read notifications failure as per original implementation
            logger.error("Failed to mark notifications as read", e)
        }
    }

    suspend fun refresh(event: ScreenEvent.Refresh) = logger.async {
        val result = status.execute(
            loading = Message.fromString("Refreshing notifications..."),
            success = Message.fromString(""),
            job = { refreshJob(event) }
        )

        if (result is AsyncResult.Fail) {
            sideEffect.emit(SideEffect.ShowError(result.error.message.text()))
        }
    }

    private suspend fun refreshJob(event: ScreenEvent.Refresh) = logger.async {
        // Reset pagination state
        screen.update { it.copy(currentPage = 1, isLastPage = false, isLoadingMore = false) }
        fetchNotifications(ScreenEvent.Init)
    }
}

package presentation.home.notifications.domain.usecases

import core.common.error.KError
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import data.keyless.notifications.NotificationsRepository
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository
import presentation.home.notifications.domain.ScreenData
import presentation.home.notifications.domain.SideEffect
import presentation.home.notifications.domain.UserEvent
import presentation.home.notifications.domain.notificationsGetRequest

internal class LoadMoreNotificationsUseCase(
    private val notificationsRepository: NotificationsRepository,
    private val screen: ScreenDataRepository<ScreenData>,
    private val sideEffect: SideEffectsRepository<SideEffect>,
    private val logger: Logger,
    private val status: StatusRepository
) {

    suspend fun execute(event: UserEvent.LoadMoreNotifications) = logger.async {
        val currentData = screen.data.value
        
        // Prevent multiple simultaneous pagination requests
        if (currentData.isLoadingMore || currentData.isLastPage) {
            return@async
        }

        // Set loading state
        screen.update { it.copy(isLoadingMore = true) }

        val result = status.execute(
            loading = Message.fromString(""),
            success = Message.fromString(""),
            job = { job(event, currentData.currentPage + 1) }
        )

        if (result is AsyncResult.Fail) {
            // Reset loading state on failure
            screen.update { it.copy(isLoadingMore = false) }
            sideEffect.emit(SideEffect.ShowError(result.error.message.text()))
        }
    }

    private suspend fun job(event: UserEvent.LoadMoreNotifications, nextPage: Int) = logger.async {
        fetchMoreNotifications(event, nextPage)
    }

    private suspend fun fetchMoreNotifications(event: UserEvent.LoadMoreNotifications, nextPage: Int) = logger.async {
        val request = event.notificationsGetRequest(nextPage)
        val response = notificationsRepository.getNotifications(request)
        
        if (response.success) {
            handleLoadMoreSuccess(response, nextPage)
        } else {
            throw KError.Info(Message.fromString("Failed to load more notifications"))
        }
    }

    private suspend fun handleLoadMoreSuccess(
        response: data.keyless.notifications.NotificationsGetResponse,
        nextPage: Int
    ) = logger.async {
        val currentData = screen.data.value
        val allNotifications = currentData.notifications + response.notifications
        val isLastPage = allNotifications.size >= response.total

        screen.update { currentData ->
            currentData.copy(
                notifications = allNotifications,
                currentPage = nextPage,
                isLastPage = isLastPage,
                isLoadingMore = false,
                totalNotifications = response.total
            )
        }
    }
}

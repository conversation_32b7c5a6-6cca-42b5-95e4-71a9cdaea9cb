package presentation.home.dashboard.feature

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import data.common.preferences.Preferences
import data.keyless.home.LockSummary
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel
import presentation.authentication.AuthenticationActivity
import presentation.authentication.AuthenticationFlow
import presentation.common.feature.android.dialPhone
import presentation.common.feature.android.openUrl
import presentation.common.feature.android.whatsApp
import presentation.common.feature.components.AppAlertDialogView
import presentation.common.feature.theme.AppTheme
import presentation.home.dashboard.domain.ScreenEvent
import presentation.home.dashboard.domain.SideEffect

class DashboardFragment : Fragment() {

    private val viewModel: DashboardAndroidViewModel by viewModel()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        return ComposeView(requireContext()).apply { setContent { AppTheme { Screen() } } }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) { viewModel.sideEffects.collect(::onSideEffect) }
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.onEvent(ScreenEvent.Init)
    }

    private fun onSideEffect(sideEffect: SideEffect) = when (sideEffect) {
        is SideEffect.NavToChangePassword -> navToChangePassword()
        is SideEffect.NavToLockDetails -> navigateToLockDetails(sideEffect.lockSummary)
        is SideEffect.OpenUrl -> requireContext().openUrl(sideEffect.url)
        is SideEffect.Dial -> requireContext().dialPhone(sideEffect.phoneNumber)
        is SideEffect.OpenWhatsApp -> requireContext().whatsApp(sideEffect.phoneNumber)
        is SideEffect.Logout -> logout()
    }

    private fun navigateToLockDetails(lockSummary: LockSummary) {
        if (LOCK_DETAILS_ACTIVITY_CLASS == null) return
        val intent = Intent(requireContext(), LOCK_DETAILS_ACTIVITY_CLASS).putExtra("lockSummary", lockSummary)
        startActivity(intent)
    }

    private fun navToChangePassword() {
        val intent = Intent(requireContext(), AuthenticationActivity::class.java)
            .putExtra(AuthenticationActivity.AUTHENTICATION_FLOW_KEY, AuthenticationFlow.UPDATE_PASSWORD)
        requireActivity().startActivity(intent)
    }

    private fun logout() {
        AppAlertDialogView(this, getString(keyless.presentation.common.R.string.login_again_here_to_continue)) {
            Preferences.isLoggedInAnotherDevice.set(true)
            Preferences.clear()
            requireActivity().recreate()
        }
    }

    @Composable
    private fun Screen() {
        DashboardScreen(state = viewModel.state, onEvent = viewModel::onEvent)
    }

    companion object {
        var LOCK_DETAILS_ACTIVITY_CLASS: Class<*>? = null
    }
}

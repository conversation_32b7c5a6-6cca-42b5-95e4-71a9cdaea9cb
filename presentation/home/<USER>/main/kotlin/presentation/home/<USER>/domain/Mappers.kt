package presentation.home.notifications.domain

import data.common.preferences.Preferences
import data.keyless.notifications.NotificationsGetRequest
import data.keyless.notifications.ReadNotificationsPostRequest

internal fun ScreenEvent.Init.notificationsGetRequest() = NotificationsGetRequest(
    page = 1,
    token = Preferences.authenticationToken.get()
)

internal fun UserEvent.LoadMoreNotifications.notificationsGetRequest(page: Int) = NotificationsGetRequest(
    page = page,
    token = Preferences.authenticationToken.get()
)

internal fun ScreenEvent.Refresh.notificationsGetRequest() = NotificationsGetRequest(
    page = 1,
    token = Preferences.authenticationToken.get()
)

internal fun readNotificationsRequest() = ReadNotificationsPostRequest(
    token = Preferences.authenticationToken.get()
)

package presentation.home.notifications.feature

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import keyless.presentation.common.R
import presentation.common.feature.components.AppColumn
import presentation.common.feature.components.AppIcon
import presentation.common.feature.components.AppImage
import presentation.common.feature.components.AppRow

@Composable
internal fun NotificationsToolbar(
    onBackClick: () -> Unit = {}
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(colorResource(R.color.colorAccent)) // colorAccent blue
            .padding(top = 10.dp)
    ) {
        AppRow(
            modifier = Modifier.fillMaxWidth(),
            alignment = Alignment.CenterVertically
        ) {
            // Back button
            AppIcon(
                res = R.drawable.iv_back_white,
                modifier = Modifier
                    .clickable { onBackClick() }
                    .padding(20.dp)
                    .size(10.dp, 19.dp),
                tint = Color.White
            )

            Spacer(modifier = Modifier.weight(1f))

            // Title
            Text(
                text = stringResource(R.string.notifications),
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color.White
            )

            Spacer(modifier = Modifier.weight(1f))

            // Empty space to balance the back button
            Spacer(modifier = Modifier.size(50.dp))
        }
    }
}

@Composable
internal fun NotificationItem(
    content: String,
    date: String,
    isUnread: Boolean = false,
    modifier: Modifier = Modifier
) {
    AppColumn(
        modifier = modifier.fillMaxWidth(),
        arrangement = Arrangement.Top
    ) {
        // Notification content
        Text(
            text = content,
            fontSize = 14.sp,
            fontWeight = if (isUnread) FontWeight.Bold else FontWeight.Medium,
            color = colorResource(R.color.black),
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 16.dp)
        )

        // Date
        Text(
            text = date,
            fontSize = 12.sp,
            fontWeight = FontWeight.Normal,
            color = colorResource(R.color.black),
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .padding(top = 4.dp)
        )

        // Divider line
        HorizontalDivider(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            thickness = 1.dp,
            color = colorResource(R.color.line_bg_color)
        )
    }
}

@Composable
internal fun NotificationsEmptyState(
    modifier: Modifier = Modifier
) {
    AppColumn(
        modifier = modifier.fillMaxWidth(),
        alignment = Alignment.CenterHorizontally,
        arrangement = Arrangement.Center
    ) {
        // No notifications image
        AppImage(
            res = R.drawable.no_notifications,
            modifier = Modifier.size(212.dp)
        )

        Spacer(modifier = Modifier.height(12.dp))

        // No notifications text
        Text(
            text = stringResource(R.string.no_notifications),
            fontSize = 14.sp,
            fontWeight = FontWeight.SemiBold,
            color = colorResource(R.color.black)
        )
    }
}

@Composable
internal fun PaginationProgress(
    isVisible: Boolean,
    modifier: Modifier = Modifier
) {
    if (isVisible) {
        Box(
            modifier = modifier.fillMaxWidth(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator(
                modifier = Modifier
                    .size(24.dp)
                    .padding(bottom = 12.dp),
                color = colorResource(R.color.blue) // progressBarBlue theme color
            )
        }
    }
}

// TODO: Hardcoded notification data for now - will be replaced with real data later
@Composable
internal fun SampleNotificationData(): List<NotificationItemData> {
    return listOf(
        NotificationItemData(
            content = "Your lock battery is low. Please replace the battery soon.",
            date = "2 hours ago",
            isUnread = true
        ),
        NotificationItemData(
            content = "New guest access has been granted for Unit 101.",
            date = "1 day ago",
            isUnread = false
        ),
        NotificationItemData(
            content = "Lock maintenance scheduled for tomorrow at 10 AM.",
            date = "2 days ago",
            isUnread = false
        )
    )
}

// TODO: Data class for notification items - will be moved to domain layer later
data class NotificationItemData(
    val content: String,
    val date: String,
    val isUnread: Boolean
)

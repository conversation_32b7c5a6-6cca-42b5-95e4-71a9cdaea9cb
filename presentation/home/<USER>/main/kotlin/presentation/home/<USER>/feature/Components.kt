package presentation.home.dashboard.feature

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.bumptech.glide.integration.compose.GlideImage
import data.common.utils.ensureEndsWith
import data.keyless.home.LockDetails
import data.keyless.home.LockSummary
import data.keyless.home.PropertyDetails
import keyless.presentation.common.R
import keyless.presentation.home.ConfigValues
import presentation.common.feature.components.AppCarouselIndicator
import presentation.common.feature.components.AppColumn
import presentation.common.feature.components.AppIcon
import presentation.common.feature.components.AppImage
import presentation.common.feature.components.AppLabelText
import presentation.common.feature.components.AppPageTitleText
import presentation.common.feature.components.AppRow
import presentation.common.feature.components.AppSurface
import presentation.common.feature.components.DesignSystem
import presentation.common.feature.components.hasUrl
import presentation.common.feature.components.hiddenClickable
import presentation.common.feature.components.loadingAlpha
import presentation.common.feature.components.toDp
import presentation.home.dashboard.domain.Event
import presentation.home.dashboard.domain.UserEvent
import presentation.home.dashboard.test.TestTags

@Composable
internal fun RowScope.GuestTopBarContent(state: StateHolder) {
    GuestTopBarTitle(state)

    GuestTopBarNotificationButton(state, state.ui.notificationCount.value)

    GuestTopBarSettingsButton(state)
}

@Composable
private fun GuestTopBarSettingsButton(screen: StateHolder) {
    AppIcon(
        modifier = Modifier
            .testTag(TestTags.settingsButton)
            .hiddenClickable(enabled = !screen.ui.isLoading.value) {},
        res = R.drawable.iv_settings
    )
}

@Composable
private fun GuestTopBarNotificationButton(screen: StateHolder, notificationCount: Int) {
    Box(
        modifier = Modifier
            .testTag(TestTags.notificationButton)
            .hiddenClickable(enabled = !screen.ui.isLoading.value) {}
    ) {
        AppIcon(res = R.drawable.iv_notifications)

        if (notificationCount > 0) NotificationCount(modifier = Modifier.align(Alignment.BottomEnd), notificationCount)
    }
}

@Composable
private fun NotificationCount(modifier: Modifier = Modifier, count: Int) {
    var size by remember { mutableStateOf(IntSize.Zero) }
    Box(modifier = modifier.padding(start = 12.dp), contentAlignment = Alignment.Center) {
        if (size.width != 0) AppSurface(
            modifier = Modifier
                .height(size.height.toDp())
                .widthIn(maxOf(size.height.toDp(), size.width.toDp())),
            shape = CircleShape,
            color = MaterialTheme.colorScheme.error,
            content = {}
        )
        AppLabelText(
            modifier = Modifier.onGloballyPositioned { size = it.size },
            text = count.toString(),
            design = DesignSystem.Text.Label.copy(color = Color.White)
        )
    }
}

@Composable
private fun RowScope.GuestTopBarTitle(state: StateHolder) {
    AppPageTitleText(
        modifier = Modifier
            .weight(1f)
            .testTag(TestTags.topBarTitle),
        text = state.ui.welcomeText(),
        design = DesignSystem.Text.PageTitle.copy(overflow = TextOverflow.Ellipsis)
    )
}

@Composable
internal fun ClaimYourKeyButton(state: StateHolder, onClaimKey: () -> Unit) {
    AppLabelText(
        modifier = Modifier
            .loadingAlpha(state.ui.isLoading.value)
            .clickable(onClick = onClaimKey)
            .testTag(TestTags.claimKeyButton),
        text = stringResource(R.string.claim_your_key),
        design = DesignSystem.Text.Label.copy(color = MaterialTheme.colorScheme.primary, weight = FontWeight.Bold)
    )
}

@Composable
internal fun YourKeysText() {
    AppLabelText(
        modifier = Modifier.testTag(TestTags.yourKeysText),
        text = stringResource(R.string.your_keys),
        design = DesignSystem.Text.Label.copy(weight = FontWeight.Bold)
    )
}

@Composable
internal fun ChangePasswordText(state: ChangePasswordState.Show) {
    AppLabelText(
        modifier = Modifier.fillMaxWidth(),
        text = stringResource(R.string.tap_to_update_your_password_before_date, state.dueDate),
        design = DesignSystem.Text.Label.copy( alignment = TextAlign.Center)
    )
}

@Composable
internal fun LockItem(
    modifier: Modifier = Modifier,
    screen: StateHolder,
    lock: LockSummary,
    onEvent: (Event) -> Unit = {}
) {
    AppSurface(
        modifier = modifier
            .border(1.dp, MaterialTheme.colorScheme.outlineVariant, MaterialTheme.shapes.medium)
            .loadingAlpha(screen.ui.isLoading.value)
            .clickable(!screen.ui.isLoading.value) { onEvent(UserEvent.OnLockClick(lock)) }
            .testTag(TestTags.lockItem(lock.lock.id)),
        paddings = PaddingValues(0.dp),
        color = Color.Transparent,
        shape = MaterialTheme.shapes.medium
    ) {
        LockItemLayout(lock)

        if (!lock.isLockActiveForUser) LockItemInActive()
    }
}

@Composable
private fun LockItemLayout(lock: LockSummary) {
    AppColumn(
        modifier = Modifier
            .fillMaxWidth()
            .padding(DesignSystem.Padding.screen),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium),
        alignment = Alignment.CenterHorizontally
    ) {
        LockItemLockIcon(lock.lock)

        LockItemLockName(lock)

        LockItemLockAddress(lock.propertyDetails)

        LockItemPropertyName(lock)
    }
}

@Composable
private fun LockItemLockName(lock: LockSummary) {
    AppLabelText(
        text = lock.lock.name,
        design = DesignSystem.Text.Label.copy(alignment = TextAlign.Center, weight = FontWeight.Bold)
    )
}

@Composable
private fun LockItemPropertyName(lock: LockSummary) {
    AppLabelText(
        text = lock.propertyDetails.name, design = DesignSystem.Text.Label.copy(alignment = TextAlign.Center)
    )
}

@Composable
private fun LockItemLockAddress(property: PropertyDetails) {
    val context = LocalContext.current
    val place = remember(property) {
        val comma = context.getString(R.string.comma)
        if (property.appartmentNumber.isEmpty()) property.floor + " " + context.getString(R.string.floor)
        else property.appartmentNumber + "$comma " + property.floor + " " + context.getString(R.string.floor)
    }

    AppLabelText(text = place, design = DesignSystem.Text.Label.copy(alignment = TextAlign.Center))
}

@Composable
private fun LockItemInActive() {
    Box(
        modifier = Modifier
            .size(40.dp)
            .rotate(-45f),
        contentAlignment = Alignment.Center
    ) {
        LockItemInactiveRibbon()

        LockItemInactiveText()
    }
}

@Composable
private fun LockItemInactiveText() {
    AppLabelText(
        text = stringResource(R.string.inactive),
        design = DesignSystem.Text.Label.copy(color = MaterialTheme.colorScheme.onPrimary, size = 8.sp)
    )
}

@Composable
private fun LockItemInactiveRibbon() {
    AppSurface(
        modifier = Modifier
            .width(40.dp)
            .height(12.dp)
            .scale(scaleX = 2f, scaleY = 1f)
            .background(MaterialTheme.colorScheme.primary),
        content = {}
    )
}

@OptIn(ExperimentalGlideComposeApi::class)
@Composable
private fun LockItemLockIcon(lock: LockDetails) {
    val icon = remember { lock.icon.firstOrNull()?.icon }
    if (icon?.isNotBlank() == true) {
        GlideImage(
            model = ConfigValues.imageUrl.ensureEndsWith("/") + icon,
            contentDescription = null,
            modifier = Modifier
                .size(DesignSystem.Icon.medium)
                .testTag(TestTags.lockItemIcon(lock.id, icon))
        )
    } else {
        LockItemIconPlaceholder()
    }
}

@Composable
private fun LockItemIconPlaceholder() {
    AppIcon(
        modifier = Modifier.size(DesignSystem.Icon.medium),
        res = R.drawable.iv_other_icon,
        tint = MaterialTheme.colorScheme.primary
    )
}

@Composable
internal fun GuestCarouselItem(modifier: Modifier = Modifier, index: Int, titleRes: Int, textRes: Int) {
    AppSurface(
        modifier = modifier.testTag(TestTags.guestCarouselItem(index)),
        paddings = PaddingValues(DesignSystem.Padding.screen),
        color = MaterialTheme.colorScheme.tertiary
    ) {
        GuestCarouselItemLayout(titleRes, textRes)
    }
}

@Composable
internal fun GuestCarouselIndicator(index: Int) {
    AppCarouselIndicator(
        count = 3,
        currentPage = index,
        color = MaterialTheme.colorScheme.primary,
        dotTestTag = { TestTags.guestCarouselItemIndicatorIndex(it) }
    )
}

@Composable
private fun GuestCarouselItemLayout(titleRes: Int, textRes: Int) {
    AppRow(
        modifier = Modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.medium),
        alignment = Alignment.CenterVertically
    ) {
        GuestCarouselItemLogo()

        GuestCarouselItemTextArea(titleRes, textRes)
    }
}

@Composable
private fun RowScope.GuestCarouselItemTextArea(titleRes: Int, textRes: Int) {
    AppColumn(
        modifier = Modifier.weight(1f),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.small),
        alignment = Alignment.CenterHorizontally
    ) {
        GuestCarouselItemTitle(titleRes)

        GuestCarouselItemBody(textRes)
    }
}

@Composable
private fun GuestCarouselItemLogo() {
    AppImage(res = R.drawable.keyless_logo_2)
}

@Composable
private fun GuestCarouselItemBody(textRes: Int) {
    AppLabelText(
        text = stringResource(textRes),
        design = DesignSystem.Text.Label.copy(alignment = TextAlign.Center, minLines = 3)
    )
}

@Composable
private fun GuestCarouselItemTitle(titleRes: Int) {
    AppPageTitleText(text = stringResource(titleRes))
}

@Composable
internal fun QuickServicesText() {
    AppLabelText(
        modifier = Modifier
            .padding(horizontal = DesignSystem.Padding.screen)
            .testTag(TestTags.quickServicesText),
        text = stringResource(R.string.quick_services),
        design = DesignSystem.Text.Label.copy(weight = FontWeight.Bold)
    )
}

@Composable
internal fun QuickServiceItems(index: Int, state: StateHolder, onEvent: (Event) -> Unit) {
    when (index) {
        0 -> QuickServiceItemTaxi(state)
        1 -> QuickServiceItemFood(onEvent)
        2 -> QuickServiceItemHouseKeeping(onEvent)
        3 -> QuickServiceItemEmergency(state, onEvent)
        4 -> QuickServiceItemLocalExperiences(onEvent)
        5 -> QuickServiceItemGroceries(state)
        6 -> QuickServiceItemHomeHealthCare(state, onEvent)
    }
}

@Composable
private fun QuickServiceItemHomeHealthCare(
    state: StateHolder,
    onEvent: (Event) -> Unit
) {
    QuickServiceItem(
        modifier = Modifier.testTag(TestTags.quickServiceItemHomeHealthCare),
        imageRes = R.mipmap.health_care,
        textRes = R.string.home_health_care,
        onClick = {
            state.ui.callBottomSheet.update(CallBottomSheetState.Show(CallBottomSheetState.Type.Home_Health_Care))
        }
    )
}

@Composable
private fun QuickServiceItemGroceries(state: StateHolder) {
    QuickServiceItem(
        modifier = Modifier.testTag(TestTags.quickServiceItemGroceries),
        imageRes = R.mipmap.groceries_image,
        textRes = R.string.groceries,
        onClick = { state.ui.groceriesDialog.update(GroceriesDialogState.Show(GroceriesDialogState.Type.Groceries)) }
    )
}

@Composable
private fun QuickServiceItemLocalExperiences(onEvent: (Event) -> Unit) {
    val url = remember { "https://lokalee.app/locations/explore-dubai" }
    QuickServiceItem(
        modifier = Modifier.testTag(TestTags.quickServiceItemLocalExperiences).semantics { this.hasUrl = url },
        imageRes = R.mipmap.local_experinces,
        textRes = R.string.local_experiences,
        onClick = {
            onEvent(UserEvent.LogService("concierge"))
            onEvent(UserEvent.OpenUrl(url))
        }
    )
}

@Composable
private fun QuickServiceItemEmergency(state: StateHolder, onEvent: (Event) -> Unit) {
    QuickServiceItem(
        modifier = Modifier.testTag(TestTags.quickServiceItemEmergency),
        imageRes = R.mipmap.ambulance_image,
        textRes = R.string.emergency_services,
        onClick = {
            state.ui.callBottomSheet.update(CallBottomSheetState.Show(CallBottomSheetState.Type.Emergency_Services))
        }
    )
}

@Composable
private fun QuickServiceItemHouseKeeping(onEvent: (Event) -> Unit) {
    val url = remember { "https://m.urbancompany.com/ImbW/2mv89x75" }
    QuickServiceItem(
        modifier = Modifier.testTag(TestTags.quickServiceItemHouseKeeping).semantics { this.hasUrl = url },
        imageRes = R.mipmap.house_keeping_image,
        textRes = R.string.house_keeping,
        onClick = {
            onEvent(UserEvent.LogService("house_keeping"))
            onEvent(UserEvent.OpenUrl(url))
        }
    )
}

@Composable
private fun QuickServiceItemFood(onEvent: (Event) -> Unit) {
    val url = remember { "https://deliveroo.onelink.me/9Aoc/keyless" }
    QuickServiceItem(
        modifier = Modifier.testTag(TestTags.quickServiceItemFood).semantics { this.hasUrl = url  },
        imageRes = R.mipmap.delivery_image,
        textRes = R.string.food_delivery,
        onClick = {
            onEvent(UserEvent.LogService("food_delivery"))
            onEvent(UserEvent.OpenUrl(url))
        }
    )
}

@Composable
private fun QuickServiceItemTaxi(state: StateHolder) {
    QuickServiceItem(
        modifier = Modifier.testTag(TestTags.quickServiceItemTaxi),
        imageRes = R.mipmap.taxi_image,
        textRes = R.string.taxi,
        onClick = { state.ui.groceriesDialog.update(GroceriesDialogState.Show(GroceriesDialogState.Type.Taxi)) }
    )
}

@Composable
internal fun QuickServiceItem(modifier: Modifier = Modifier, imageRes: Int, textRes: Int, onClick: () -> Unit) {
    AppSurface(
        modifier = modifier.hiddenClickable(onClick = onClick),
        color = MaterialTheme.colorScheme.primary,
        contentColor = MaterialTheme.colorScheme.onPrimary,
        shape = MaterialTheme.shapes.medium
    ) {
        QuickServiceItemLayout(imageRes = imageRes, textRes = textRes)
    }
}

@Composable
internal fun QuickServiceItemLayout(modifier: Modifier = Modifier, imageRes: Int, textRes: Int) {
    AppColumn(
        modifier = modifier
            .size(160.dp)
            .padding(bottom = DesignSystem.Padding.small),
        arrangement = Arrangement.spacedBy(DesignSystem.Padding.small),
        alignment = Alignment.CenterHorizontally
    ) {
        QuickServicesItemImage(modifier = Modifier.weight(1f), imageRes)

        QuickServicesItemText(textRes)
    }
}

@Composable
private fun QuickServicesItemText(textRes: Int) {
    AppLabelText(text = stringResource(textRes))
}

@Composable
private fun QuickServicesItemImage(modifier: Modifier = Modifier, imageRes: Int) {
    AppImage(res = imageRes, contentScale = ContentScale.Crop, modifier = modifier)
}
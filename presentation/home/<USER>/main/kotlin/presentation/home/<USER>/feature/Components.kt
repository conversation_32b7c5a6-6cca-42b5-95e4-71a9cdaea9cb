package presentation.home.notifications.feature

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import data.keyless.notifications.NotificationItem
import keyless.presentation.common.R
import presentation.common.feature.components.AppBodyText
import presentation.common.feature.components.AppColumn
import presentation.common.feature.components.AppHorizontalDivider
import presentation.common.feature.components.AppImage
import presentation.common.feature.components.AppLabelText
import presentation.common.feature.components.DesignSystem

@Composable
internal fun NotificationItemContent(
    notification: NotificationItem,
    modifier: Modifier = Modifier
) {
    AppColumn(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = DesignSystem.Padding.screen, vertical = DesignSystem.Padding.medium),
        arrangement = Arrangement.Top
    ) {
        NotificationText(content = notification.content, isUnread = notification.status == 1)

        NotificationDate(date = notification.createdAt)

        NotificationDivider()
    }
}

@Composable
private fun NotificationText(
    content: String,
    isUnread: Boolean
) {
    if (isUnread) {
        AppLabelText(
            text = content,
            modifier = Modifier.fillMaxWidth(),
            design = DesignSystem.Text.Label.copy(
                weight = FontWeight.Bold,
                color = colorResource(R.color.black)
            )
        )
    } else {
        AppBodyText(
            text = content,
            modifier = Modifier.fillMaxWidth(),
            design = DesignSystem.Text.Body.copy(
                weight = FontWeight.Medium,
                color = colorResource(R.color.black)
            )
        )
    }
}

@Composable
private fun NotificationDate(
    date: String
) {
    // TODO: Format date using CommonValues.formateTimeDate when available
    val formattedDate = date.split(".").firstOrNull() ?: date

    AppLabelText(
        text = formattedDate,
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = DesignSystem.Padding.small),
        design = DesignSystem.Text.Label.copy(
            weight = FontWeight.Normal,
            color = colorResource(R.color.black),
            size = 12.sp
        )
    )
}

@Composable
private fun NotificationDivider() {
    AppColumn(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = DesignSystem.Padding.medium)
    ) {
        AppHorizontalDivider()
    }
}

@Composable
internal fun NotificationsEmptyState(
    modifier: Modifier = Modifier
) {
    AppColumn(
        modifier = modifier,
        alignment = Alignment.CenterHorizontally,
        arrangement = Arrangement.Center
    ) {
        EmptyStateImage()
        EmptyStateText()
    }
}

@Composable
private fun EmptyStateImage() {
    AppImage(
        res = R.drawable.no_notifications,
        modifier = Modifier.size(212.dp)
    )
}

@Composable
private fun EmptyStateText() {
    Spacer(modifier = Modifier.height(DesignSystem.Padding.medium))

    AppLabelText(
        text = stringResource(R.string.no_notifications),
        design = DesignSystem.Text.Label.copy(
            weight = FontWeight.SemiBold,
            color = colorResource(R.color.black)
        )
    )
}

@Composable
internal fun PaginationProgressIndicator(
    isVisible: Boolean,
    modifier: Modifier = Modifier
) {
    if (isVisible) {
        PaginationProgress(modifier = modifier)
    }
}

@Composable
private fun PaginationProgress(
    modifier: Modifier = Modifier
) {
    AppColumn(
        modifier = modifier.fillMaxWidth(),
        alignment = Alignment.CenterHorizontally
    ) {
        CircularProgressIndicator(
            modifier = Modifier
                .size(DesignSystem.Icon.small)
                .padding(bottom = DesignSystem.Padding.medium),
            color = colorResource(R.color.blue)
        )
    }
}

// TODO: Sample notification data - will be replaced with real data from domain layer
internal fun getSampleNotifications(): List<NotificationItem> {
    return listOf(
        NotificationItem(
            content = "Your lock battery is low. Please replace the battery soon.",
            createdAt = "2024-01-15T10:30:00.000Z",
            status = 1 // unread
        ),
        NotificationItem(
            content = "New guest access has been granted for Unit 101.",
            createdAt = "2024-01-14T15:45:00.000Z",
            status = 0 // read
        ),
        NotificationItem(
            content = "Lock maintenance scheduled for tomorrow at 10 AM.",
            createdAt = "2024-01-13T09:15:00.000Z",
            status = 0 // read
        )
    )
}

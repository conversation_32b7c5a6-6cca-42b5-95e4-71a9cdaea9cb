package presentation.home.notifications.domain

import core.common.status.Status
import data.keyless.notifications.NotificationItem

data class ScreenData(
    val notifications: List<NotificationItem>,
    val currentPage: Int,
    val isLastPage: <PERSON>olean,
    val isLoadingMore: Boolean,
    val totalNotifications: Int,
    val status: List<Status>
) {
    companion object {
        val empty = ScreenData(
            notifications = emptyList(),
            currentPage = 1,
            isLastPage = false,
            isLoadingMore = false,
            totalNotifications = 0,
            status = emptyList()
        )
    }
    
    val isEmpty: Boolean get() = notifications.isEmpty()
    val hasNotifications: <PERSON>olean get() = notifications.isNotEmpty()
}

sealed interface SideEffect {
    object NavigateBack : SideEffect
    data class ShowError(val message: String) : SideEffect
}

sealed interface Event

sealed interface UserEvent : Event {
    object OnBackClick : UserEvent
    object LoadMoreNotifications : UserEvent
}

sealed interface ScreenEvent : Event {
    object Init : ScreenEvent
    object Refresh : ScreenEvent
    data class DismissStatus(val status: Status) : ScreenEvent
}

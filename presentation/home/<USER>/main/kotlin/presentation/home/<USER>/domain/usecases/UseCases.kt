package presentation.home.notifications.domain.usecases

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import data.keyless.notifications.NotificationsRepository
import presentation.common.domain.repositories.ScreenDataRepository
import presentation.common.domain.repositories.SideEffectsRepository
import presentation.home.notifications.domain.ScreenData
import presentation.home.notifications.domain.SideEffect

internal class UseCases(
    private val notificationsRepository: NotificationsRepository,
    private val screenData: ScreenDataRepository<ScreenData>,
    private val sideEffects: SideEffectsRepository<SideEffect>,
    private val logger: Logger,
    private val status: StatusRepository
) {

    val initScreen = InitScreenUseCase(
        notificationsRepository = notificationsRepository,
        screen = screenData,
        sideEffect = sideEffects,
        logger = logger,
        status = status
    )

    val loadMoreNotifications = LoadMoreNotificationsUseCase(
        notificationsRepository = notificationsRepository,
        screen = screenData,
        sideEffect = sideEffects,
        logger = logger,
        status = status
    )

    val onBackClick = OnBackClickUseCase(
        sideEffect = sideEffects,
        logger = logger
    )
}

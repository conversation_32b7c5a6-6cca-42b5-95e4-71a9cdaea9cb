package presentation.home.notifications.feature

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import keyless.presentation.common.R
import presentation.common.feature.components.AppColumn

@Composable
fun NotificationsScreen(
    onBackClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    // TODO: Replace with real state management later
    var notifications by remember { mutableStateOf(SampleNotificationData()) }
    var isLoading by remember { mutableStateOf(false) }
    var isPaginationLoading by remember { mutableStateOf(false) }
    var currentPage by remember { mutableStateOf(1) }
    var isLastPage by remember { mutableStateOf(false) }
    
    val listState = rememberLazyListState()
    
    // Check if we should load more items (pagination)
    val shouldLoadMore by remember {
        derivedStateOf {
            val lastVisibleItem = listState.layoutInfo.visibleItemsInfo.lastOrNull()
            lastVisibleItem != null && 
            lastVisibleItem.index >= notifications.size - 3 && 
            !isPaginationLoading && 
            !isLastPage
        }
    }
    
    // Trigger pagination when needed
    LaunchedEffect(shouldLoadMore) {
        if (shouldLoadMore) {
            isPaginationLoading = true
            // TODO: Load more notifications from API
            // For now, simulate loading delay
            kotlinx.coroutines.delay(1000)
            isPaginationLoading = false
        }
    }

    AppColumn(
        modifier = modifier
            .fillMaxSize()
            .background(colorResource(R.color.colorAccent)) // colorAccent blue background
    ) {
        // Toolbar
        NotificationsToolbar(
            onBackClick = onBackClick
        )
        
        // Content area with white rounded top corners
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = 15.dp)
                .clip(RoundedCornerShape(topStart = 32.dp, topEnd = 32.dp))
                .background(colorResource(R.color.white))
        ) {
            if (notifications.isEmpty() && !isLoading) {
                // Empty state
                NotificationsEmptyState(
                    modifier = Modifier.align(Alignment.Center)
                )
            } else {
                // Notifications list
                NotificationsList(
                    notifications = notifications,
                    listState = listState,
                    isPaginationLoading = isPaginationLoading,
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
    }
}

@Composable
private fun NotificationsList(
    notifications: List<NotificationItemData>,
    listState: androidx.compose.foundation.lazy.LazyListState,
    isPaginationLoading: Boolean,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        state = listState,
        modifier = modifier.padding(top = 5.dp)
    ) {
        items(notifications) { notification ->
            NotificationItem(
                content = notification.content,
                date = notification.date,
                isUnread = notification.isUnread
            )
        }
        
        // Pagination progress indicator
        if (isPaginationLoading) {
            item {
                PaginationProgress(
                    isVisible = true,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

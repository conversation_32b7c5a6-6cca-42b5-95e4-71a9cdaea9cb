package presentation.home.notifications.feature

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import core.common.status.Status
import data.keyless.notifications.NotificationItem
import keyless.presentation.common.R
import presentation.common.feature.components.AppScrollColumn
import presentation.common.feature.components.AppTitledPage
import presentation.common.feature.theme.AppTheme

@Composable
fun NotificationsScreen(
    onBackClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    // TODO: Replace with real state management from domain layer
    val notifications = remember { getSampleNotifications() }
    val status = remember { emptyList<Status>() }

    AppTitledPage(
        modifier = modifier,
        title = stringResource(R.string.notifications),
        isBackEnabled = true,
        onBackPress = onBackClick,
        status = status,
        onCancelStatus = { }
    ) {
        NotificationsContent(
            notifications = notifications,
            modifier = Modifier.fillMaxSize()
        )
    }
}

@Composable
private fun NotificationsContent(
    notifications: List<NotificationItem>,
    modifier: Modifier = Modifier
) {
    if (notifications.isEmpty()) {
        NotificationsEmptyState(modifier = modifier)
    } else {
        NotificationsList(
            notifications = notifications,
            modifier = modifier
        )
    }
}

@Composable
private fun NotificationsList(
    notifications: List<NotificationItem>,
    modifier: Modifier = Modifier
) {
    // TODO: Add pagination state management from domain layer
    val listState = rememberLazyListState()
    val isPaginationLoading = remember { mutableStateOf(false) }

    AppScrollColumn(
        state = listState,
        modifier = modifier
    ) {
        items(notifications) { notification ->
            NotificationItemContent(
                notification = notification,
                modifier = Modifier.fillMaxWidth()
            )
        }

        // TODO: Add pagination logic when domain layer is connected
        if (isPaginationLoading.value) {
            item {
                PaginationProgressIndicator(
                    isVisible = true,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

@Preview
@Composable
private fun Preview() {
    AppTheme { NotificationsScreen(onBackClick = {}) }
}
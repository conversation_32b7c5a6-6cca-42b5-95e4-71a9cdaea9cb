package presentation.test

import android.content.Context
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.test.SemanticsMatcher
import androidx.compose.ui.test.SemanticsNodeInteraction
import androidx.compose.ui.test.TouchInjectionScope
import androidx.compose.ui.test.assertHasClickAction
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertIsEnabled
import androidx.compose.ui.test.assertIsNotDisplayed
import androidx.compose.ui.test.assertIsNotEnabled
import androidx.compose.ui.test.assertIsNotSelected
import androidx.compose.ui.test.assertIsSelected
import androidx.compose.ui.test.assertTextEquals
import androidx.compose.ui.test.hasScrollAction
import androidx.compose.ui.test.hasTestTag
import androidx.compose.ui.test.hasText
import androidx.compose.ui.test.junit4.ComposeTestRule
import androidx.compose.ui.test.onNodeWithTag
import androidx.compose.ui.test.onParent
import androidx.compose.ui.test.performClick
import androidx.compose.ui.test.performImeAction
import androidx.compose.ui.test.performTextClearance
import androidx.compose.ui.test.performTextInput
import androidx.compose.ui.test.performTouchInput
import androidx.compose.ui.test.swipeDown
import androidx.compose.ui.test.swipeLeft
import androidx.compose.ui.test.swipeRight
import androidx.compose.ui.test.swipeUp
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import presentation.common.feature.components.isRtl

class ComposeTestNode {
    private val compose: ComposeTestRule
    private val node: SemanticsNodeInteraction
    val parent: ComposeTestNode get() = ComposeTestNode(compose, node.onParent())
    private var matcher: SemanticsMatcher? = null

    constructor(compose: ComposeTestRule, tag: String, unMergedTree: Boolean = false) {
        this.compose = compose
        this.matcher = hasTestTag(tag)
        node = compose.onNodeWithTag(tag, unMergedTree)
    }

    constructor(compose: ComposeTestRule, text: String, ignoreCase: Boolean = false, unMergedTree: Boolean = false) {
        this.compose = compose
        this.matcher = hasText(text, ignoreCase)
        node = compose.onNode(matcher!!, unMergedTree)
    }

    constructor(compose: ComposeTestRule, node: SemanticsNodeInteraction) {
        this.compose = compose
        this.node = node
    }

    fun displayedOrScroll(scrollableNode: SemanticsNodeInteraction = compose.onNode(hasScrollAction())): ComposeTestNode = wait {
        if (matcher != null) compose.displayedOrScrollTo(matcher!!, scrollableNode)
        else node.displayedOrScroll()
    }
    fun assertExists(): ComposeTestNode = wait { node.assertExists() }
    fun assertDoesNotExist(): ComposeTestNode = wait { node.assertDoesNotExist() }
    fun assertIsDisplayed(): ComposeTestNode = wait { node.assertIsDisplayed() }
    fun assertIsNotDisplayed(): ComposeTestNode = wait { node.assertIsNotDisplayed() }
    fun assertIsEnabled(): ComposeTestNode = wait { node.assertIsEnabled() }
    fun assertIsNotEnabled(): ComposeTestNode = wait { node.assertIsNotEnabled() }
    fun assertHasClickAction(): ComposeTestNode = wait { node.assertHasClickAction() }
    fun assertTextEquals(text: String): ComposeTestNode = wait { node.assertTextEquals(text) }

    fun assertTextColor(color: Color): ComposeTestNode = wait { node.assertTextColor(color) }
    fun assertTextSize(size: TextUnit): ComposeTestNode = wait { node.assertTextSize(size) }
    fun assertTextWeight(weight: FontWeight): ComposeTestNode = wait { node.assertTextWeight(weight) }
    fun assertTextAlignment(alignment: TextAlign): ComposeTestNode = wait { node.assertTextAlignment(alignment) }
    fun assertTextDecoration(decoration: TextDecoration): ComposeTestNode = wait { node.assertTextDecoration(decoration) }
    fun assertTextMinLines(minLines: Int): ComposeTestNode = wait { node.assertTextMinLines(minLines) }
    fun assertTextOverflow(overflow: TextOverflow): ComposeTestNode = wait { node.assertTextOverflow(overflow) }
    fun assertBackgroundColor(color: Color): ComposeTestNode = wait { node.assertBackgroundColor(color) }
    fun assertContentColor(color: Color): ComposeTestNode = wait { node.assertContentColor(color) }
    fun assertIconTint(tint: Color): ComposeTestNode = wait { node.assertIconTint(tint) }
    fun assertDrawableRes(res: Int): ComposeTestNode = wait { node.assertDrawableRes(res) }
    fun assertShape(shape: Shape): ComposeTestNode = wait { node.assertShape(shape) }
    fun assertHasUrl(url: String): ComposeTestNode = wait { node.assertHasUrl(url) }
    fun assertHasDialNumber(number: String): ComposeTestNode = wait { node.assertHasDialNumber(number) }
    fun assertHasWhatsAppNumber(number: String): ComposeTestNode = wait { node.assertHasWhatsAppNumber(number) }



    fun assertIsSelected(): ComposeTestNode = wait { node.assertIsSelected() }
    fun assertIsNotSelected(): ComposeTestNode = wait { node.assertIsNotSelected() }
    fun waitExists(timeoutMillis: Long = 5_000) = wait(timeoutMillis) { assertExists() }
    fun anyChild(block: (SemanticsNodeInteraction) -> Unit) = wait { node.anyChild { block(it) } }

    fun getChild(block: (SemanticsNodeInteraction) -> Unit) = ComposeTestNode(compose, node.getChild { block(it) })

    fun getNode(block: (ComposeTestNode) -> Unit) = getChild { block(ComposeTestNode(compose, it)) }

    fun performClick(confirmation: () -> Unit): ComposeTestNode = wait {
        node.performClick()
        confirmation()
    }

    fun performImeAction(confirmation: () -> Unit) = wait {
        node.performImeAction()
        confirmation()
    }

    fun performTouchInput(block: TouchInjectionScope.() -> Unit, confirmation: () -> Unit) = wait {
        node.performTouchInput(block)
        confirmation()
    }

    fun performTextClearance() = wait {
        node.performTextClearance()
        node.assertTextEquals("")
    }

    fun performTextInput(text: String, isPassword: Boolean = false) = wait {
        node.performTextClearance()
        node.performTextInput(text)
        node.assertTextEquals(if (!isPassword) text else text.passwordText())
    }

    fun swipeUp(confirmation: () -> Unit) = wait {
        node.performTouchInput { swipeUp() }
        confirmation()
    }

    fun swipeDown(confirmation: () -> Unit) = wait {
        node.performTouchInput { swipeDown() }
        confirmation()
    }

    fun swipeStart(context: Context, confirmation: () -> Unit) = wait {
        if (context.isRtl()) swipeRight(confirmation) else swipeLeft(confirmation)
    }

    fun swipeEnd(context: Context, confirmation: () -> Unit) = wait {
        if (context.isRtl()) swipeLeft(confirmation) else swipeRight(confirmation)
    }

    private fun swipeRight(confirmation: () -> Unit) = wait {
        node.performTouchInput { swipeRight() }
        confirmation()
    }

    private fun swipeLeft(confirmation: () -> Unit) = wait {
        node.performTouchInput { swipeLeft() }
        confirmation()
    }

    private fun String.passwordText(mask: Char = '\u2022'): String {
        return AnnotatedString(mask.toString().repeat(length)).text
    }

    private fun wait(timeoutMillis: Long = 5_000, block: () -> Unit): ComposeTestNode {
        compose.wait(timeoutMillis, block)
        return this
    }
}
package presentation.test

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.test.SemanticsMatcher
import androidx.compose.ui.test.SemanticsNodeInteraction
import androidx.compose.ui.test.assert
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import presentation.common.feature.components.BackgroundColorSemanticProperty
import presentation.common.feature.components.ContentColorSemanticProperty
import presentation.common.feature.components.DialNumberSemanticProperty
import presentation.common.feature.components.DrawableResSemanticProperty
import presentation.common.feature.components.IconTintSemanticProperty
import presentation.common.feature.components.ShapeSemanticProperty
import presentation.common.feature.components.TextAlignmentSemanticProperty
import presentation.common.feature.components.TextColorSemanticProperty
import presentation.common.feature.components.TextDecorationSemanticProperty
import presentation.common.feature.components.TextMinLinesSemanticProperty
import presentation.common.feature.components.TextOverflowSemanticProperty
import presentation.common.feature.components.TextSizeSemanticProperty
import presentation.common.feature.components.TextWeightSemanticProperty
import presentation.common.feature.components.UrlSemanticProperty
import presentation.common.feature.components.WhatsAppNumberSemanticProperty

fun SemanticsNodeInteraction.assertBackgroundColor(color: Color) = assert(hasBackgroundColor(color))
fun SemanticsNodeInteraction.assertContentColor(color: Color) = assert(hasContentColor(color))
fun SemanticsNodeInteraction.assertIconTint(tint: Color) = assert(hasIconTint(tint))
fun SemanticsNodeInteraction.assertDrawableRes(res: Int) = assert(hasIconRes(res))
fun SemanticsNodeInteraction.assertShape(shape: Shape) = assert(hasShape(shape))
fun SemanticsNodeInteraction.assertTextColor(textColor: Color) = assert(hasTextColor(textColor))
fun SemanticsNodeInteraction.assertTextSize(size: TextUnit) = assert(hasTextSize(size))
fun SemanticsNodeInteraction.assertTextWeight(weight: FontWeight) = assert(hasTextWeight(weight))
fun SemanticsNodeInteraction.assertTextAlignment(alignment: TextAlign) = assert(hasTextAlignment(alignment))
fun SemanticsNodeInteraction.assertTextDecoration(decoration: TextDecoration) = assert(hasTextDecoration(decoration))
fun SemanticsNodeInteraction.assertTextMinLines(minLines: Int) = assert(hasTextMinLines(minLines))
fun SemanticsNodeInteraction.assertTextOverflow(overflow: TextOverflow) = assert(hasTextOverflow(overflow))

fun SemanticsNodeInteraction.assertHasUrl(url: String) = assert(hasUrl(url))
fun SemanticsNodeInteraction.assertHasDialNumber(number: String) = assert(hasDialNumber(number))
fun SemanticsNodeInteraction.assertHasWhatsAppNumber(number: String) = assert(hasWhatsAppNumber(number))

private fun hasBackgroundColor(color: Color) = SemanticsMatcher.expectValue(BackgroundColorSemanticProperty, color)
private fun hasContentColor(contentColor: Color) = SemanticsMatcher.expectValue(ContentColorSemanticProperty, contentColor)
private fun hasIconTint(iconTint: Color) = SemanticsMatcher.expectValue(IconTintSemanticProperty, iconTint)
private fun hasIconRes(res: Int) = SemanticsMatcher.expectValue(DrawableResSemanticProperty, res)
private fun hasShape(shape: Shape) = SemanticsMatcher.expectValue(ShapeSemanticProperty, shape)
private fun hasTextColor(textColor: Color) = SemanticsMatcher.expectValue(TextColorSemanticProperty, textColor)
private fun hasTextSize(size: TextUnit) = SemanticsMatcher.expectValue(TextSizeSemanticProperty, size)
private fun hasTextWeight(weight: FontWeight) = SemanticsMatcher.expectValue(TextWeightSemanticProperty, weight)
private fun hasTextAlignment(alignment: TextAlign) = SemanticsMatcher.expectValue(TextAlignmentSemanticProperty, alignment)
private fun hasTextDecoration(decoration: TextDecoration) = SemanticsMatcher.expectValue(TextDecorationSemanticProperty, decoration)
private fun hasTextMinLines(minLines: Int) = SemanticsMatcher.expectValue(TextMinLinesSemanticProperty, minLines)
private fun hasTextOverflow(overflow: TextOverflow) = SemanticsMatcher.expectValue(TextOverflowSemanticProperty, overflow)

private fun hasUrl(url: String) = SemanticsMatcher.expectValue(UrlSemanticProperty, url)
private fun hasDialNumber(number: String) = SemanticsMatcher.expectValue(DialNumberSemanticProperty, number)
private fun hasWhatsAppNumber(number: String) = SemanticsMatcher.expectValue(WhatsAppNumberSemanticProperty, number)

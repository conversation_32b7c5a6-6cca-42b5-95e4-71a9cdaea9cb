package presentation.test.predefined

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import presentation.test.ComposeTestNode

fun ComposeTestNode.assertText(
    color: Color,
    size: TextUnit,
    weight: FontWeight,
    alignment: TextAlign = TextAlign.Start,
    textDecoration: TextDecoration = TextDecoration.None,
    overflow: TextOverflow = TextOverflow.Clip,
    minLines: Int = 1,
) {
    assertTextColor(color)
    assertTextSize(size)
    assertTextAlignment(alignment)
    assertTextDecoration(textDecoration)
    assertTextOverflow(overflow)
    assertTextMinLines(minLines)
    assertTextWeight(weight)
}
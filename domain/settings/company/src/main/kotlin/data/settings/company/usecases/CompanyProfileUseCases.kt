package data.settings.company.usecases

import core.common.status.StatusRepository
import core.monitoring.common.repository.Logger
import data.company.repositories.CompanyRepository
import data.settings.company.repositories.ScreenStateRepository
import data.settings.company.repositories.SideEffectsRepository
import data.settings.company.usecases.company.UpdateCompanyProfileUseCase
import data.settings.company.usecases.screen.InitScreenUseCase

internal class CompanyProfileUseCases(
    company: CompanyRepository,
    screen: ScreenStateRepository,
    sideEffects: SideEffectsRepository,
    logger: Logger,
    status: StatusRepository
) {
    val initScreen = InitScreenUseCase(company, screen, logger, status)

    val updateCompanyProfile = UpdateCompanyProfileUseCase(company, sideEffects, logger, status)
}
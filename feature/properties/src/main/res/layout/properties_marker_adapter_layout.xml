<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:card_view="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_marginStart="4dp"
    android:visibility="visible"
    android:id="@+id/mainLay"
    android:layout_marginEnd="4dp"
    android:layout_marginBottom="8dp"
    android:theme="@style/Theme.MaterialComponents.Light"
    card_view:cardBackgroundColor="@color/white"
    app:cardCornerRadius="10dp"
    card_view:cardElevation="0dp"
    card_view:cardMaxElevation="0dp"
    app:strokeColor="@color/border_color"
    app:strokeWidth="1dp"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <include
        android:layout_marginBottom="10dp"
        android:id="@+id/markerItem"
        layout="@layout/property_item_layout"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

</com.google.android.material.card.MaterialCardView>
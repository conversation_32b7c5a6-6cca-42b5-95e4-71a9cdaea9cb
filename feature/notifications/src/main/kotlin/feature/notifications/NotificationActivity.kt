package feature.notifications

import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.window.OnBackInvokedCallback
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import data.network.android.models.NotificationsDataModel
import feature.common.dialogs.ProgressDialogUtils
import data.utils.android.interfaces.PaginationScrollListener
import data.utils.android.settings.SharedPreferenceUtils
import data.utils.android.toast
import keyless.feature.notifications.databinding.ActivityNotificationBinding

class NotificationActivity : AppCompatActivity() {

    lateinit var binding: ActivityNotificationBinding
    private lateinit var adapterNotification: AdapterNotification
    lateinit var mViewModel: NotificationViewModel
    private val sharePrefs: SharedPreferenceUtils by lazy {
        SharedPreferenceUtils.getInstance(
            this
        )
    }
    var page = 1
    private val layoutManager = LinearLayoutManager(this)
    private var isLastPage: Boolean = false
    var isLoadingMore = false
    private var listTotal = ArrayList<NotificationsDataModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityNotificationBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initz()
        setAdapter()
        apiImplementation(page, true)
        observerInit()
        clickListeners()
        pagination()
    }

    private fun pagination() {
        binding.rvNotifications.addOnScrollListener(object : PaginationScrollListener(layoutManager) {
            override fun isLastPage(): Boolean {
                return isLastPage
            }

            override fun loadMoreItems() {
                if (!isLoadingMore) {
                    page++
                    apiImplementation(page, false)
                    binding.progressPagination.visibility = View.VISIBLE
                }
            }

            override fun isLoading(): Boolean {
                return isLoadingMore
            }
        })
    }

    private fun clickListeners() {
        binding.backBtn.setOnClickListener {
            setResult(54)
            finish()
        }
    }

    private fun observerInit() {
        mViewModel.progress.observe(this) {
            if (it) {
                ProgressDialogUtils.getInstance().hideProgress()
                ProgressDialogUtils.getInstance().showProgress(this, true)
            } else {
                ProgressDialogUtils.getInstance().hideProgress()
            }
        }
        mViewModel.error.observe(this) {
            toast(it)
        }

        mViewModel.getNotifications.observe(this) {
            if (page == 1) {
                listTotal.clear()
            }
            listTotal.addAll(it.notifcations)
            if (listTotal.size > 0) {
                binding.layNoData.isVisible = false
                binding.rvNotifications.isVisible = true
                if (listTotal.size == it.total) {
                    isLastPage = true
                    isLoadingMore = true
                }
                adapterNotification.updateData(listTotal)
            } else {
                binding.layNoData.isVisible = true
                binding.rvNotifications.isVisible = false
            }
            binding.progressPagination.visibility = View.GONE

            mViewModel.readNotifications(sharePrefs.token)
        }
    }

    private fun apiImplementation(page: Int, isLoading: Boolean) {
        mViewModel.getNotificationData(sharePrefs.token, page, isLoading)
    }

    private fun setAdapter() {
        binding.rvNotifications.layoutManager = layoutManager
        adapterNotification = AdapterNotification(this)
        binding.rvNotifications.adapter = adapterNotification
    }

    private fun initz() {
        mViewModel = ViewModelProvider(this)[NotificationViewModel::class.java]
        if (Build.VERSION.SDK_INT > 32) {
            onBackInvokedDispatcher.registerOnBackInvokedCallback(
                0,
                (
                    object :
                        OnBackPressedCallback(true),
                        OnBackInvokedCallback {
                        override fun handleOnBackPressed() {
                            Log.e("handleOnBackPressed", "")
                        }

                        override fun onBackInvoked() {
                            setResult(54)
                            finish()
                        }
                    }
                    )
            )
        }
    }
}